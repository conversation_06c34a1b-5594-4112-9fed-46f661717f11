"use client";

import { useEffect, useState } from "react";
import { usePathname } from "next/navigation";

type StructuredDataProps = {
  type: "Organization" | "WebSite" | "FAQPage" | "Product" | "Article" | "WebPage" | string;
  data: Record<string, unknown>;
};

export default function StructuredData({ type, data }: StructuredDataProps) {
  const [structuredData, setStructuredData] = useState<Record<string, unknown>>({});

  useEffect(() => {
    // Combine the type and data into a structured data object
    const structuredDataObj = {
      "@context": "https://schema.org",
      "@type": type,
      ...data,
    };

    setStructuredData(structuredDataObj);
  }, [type, data]);

  // Only render on the client side
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}

// Component for adding WebPage structured data to main pages
export function MainPageStructuredData() {
  const pathname = usePathname();
  const baseUrl = "https://useadmesh.com"; // Fixed: Remove www for consistency

  // Default data with required fields
  const defaultData = {
    "@type": "WebPage",
    "@id": `${baseUrl}${pathname}#webpage`,
    url: `${baseUrl}${pathname}`,
    name: "AdMesh",
    description: "AI marketing platform that connects brands with high-intent users",
    inLanguage: "en-US",
    isPartOf: {
      "@type": "WebSite",
      "@id": `${baseUrl}#website`,
      name: "AdMesh",
      url: baseUrl,
      description: "AI marketing platform that places products directly inside real conversations",
      inLanguage: "en-US",
      publisher: {
        "@type": "Organization",
        "@id": `${baseUrl}#organization`,
        name: "AdMesh",
        url: baseUrl
      }
    },
    breadcrumb: {
      "@type": "BreadcrumbList",
      itemListElement: [
        {
          "@type": "ListItem",
          position: 1,
          name: "Home",
          item: baseUrl
        }
      ]
    }
  };

  // Page-specific data with enhanced schema
  let pageData: Record<string, unknown> = {};

  if (pathname === "/" || pathname === "/brands") {
    pageData = {
      name: pathname === "/" ? "AdMesh - AI Marketing Platform" : "AdMesh for Brands",
      description: "Connect with high-intent users looking for your products. AdMesh helps brands reach users at the perfect moment when they're actively searching for solutions.",
      mainEntity: {
        "@type": "Service",
        "@id": `${baseUrl}/brands#service`,
        name: "AdMesh for Brands",
        description: "AI marketing platform that connects brands with high-intent users through AI Platforms",
        provider: {
          "@type": "Organization",
          "@id": `${baseUrl}#organization`,
          name: "AdMesh"
        },
        serviceType: "AI Marketing Platform",
        areaServed: "Worldwide",
        hasOfferCatalog: {
          "@type": "OfferCatalog",
          name: "AdMesh Brand Services",
          itemListElement: [
            {
              "@type": "Offer",
              itemOffered: {
                "@type": "Service",
                name: "AI-Powered Product Placement",
                description: "Place your products directly inside AI conversations"
              }
            }
          ]
        }
      },
      speakable: {
        "@type": "SpeakableSpecification",
        cssSelector: ["h1", ".hero-text"]
      }
    };

    // Update breadcrumb for brands page
    if (pathname === "/brands") {
      pageData.breadcrumb = {
        "@type": "BreadcrumbList",
        itemListElement: [
          {
            "@type": "ListItem",
            position: 1,
            name: "Home",
            item: baseUrl
          },
          {
            "@type": "ListItem",
            position: 2,
            name: "For Brands",
            item: `${baseUrl}/brands`
          }
        ]
      };
    }
  } else if (pathname === "/agents") {
    pageData = {
      name: "AdMesh for AI Platforms",
      description: "Join the AdMesh Agent Pioneer Program. Earn rewards by helping users discover the perfect tools and products for their needs.",
      mainEntity: {
        "@type": "Service",
        "@id": `${baseUrl}/agents#service`,
        name: "AdMesh for AI Platforms",
        description: "Monetization platform for AI Platforms through performance marketing",
        provider: {
          "@type": "Organization",
          "@id": `${baseUrl}#organization`,
          name: "AdMesh"
        },
        serviceType: "AI Platform Monetization Platform",
        areaServed: "Worldwide"
      },
      speakable: {
        "@type": "SpeakableSpecification",
        cssSelector: ["h1", ".hero-text"]
      },
      breadcrumb: {
        "@type": "BreadcrumbList",
        itemListElement: [
          {
            "@type": "ListItem",
            position: 1,
            name: "Home",
            item: baseUrl
          },
          {
            "@type": "ListItem",
            position: 2,
            name: "For AI Platforms",
            item: `${baseUrl}/agents`
          }
        ]
      }
    };
  } else if (pathname === "/users") {
    pageData = {
      name: "AdMesh for Users",
      description: "Discover AI tools and earn rewards with AdMesh. Your personal AI marketing agent that grows with every query.",
      mainEntity: {
        "@type": "Service",
        "@id": `${baseUrl}/users#service`,
        name: "AdMesh for Users",
        description: "Personal AI marketing agent that discovers products and earns rewards",
        provider: {
          "@type": "Organization",
          "@id": `${baseUrl}#organization`,
          name: "AdMesh"
        },
        serviceType: "Personal AI Marketing Agent",
        areaServed: "Worldwide"
      },
      speakable: {
        "@type": "SpeakableSpecification",
        cssSelector: ["h1", ".hero-text"]
      },
      breadcrumb: {
        "@type": "BreadcrumbList",
        itemListElement: [
          {
            "@type": "ListItem",
            position: 1,
            name: "Home",
            item: baseUrl
          },
          {
            "@type": "ListItem",
            position: 2,
            name: "For Users",
            item: `${baseUrl}/users`
          }
        ]
      }
    };
  }

  const structuredData = {
    "@context": "https://schema.org",
    ...defaultData,
    ...pageData
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData, null, 0) }}
    />
  );
}

