import { MetadataRoute } from 'next';
import { client } from '@/lib/sanity';

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = 'https://useadmesh.com';
  const currentDate = new Date().toISOString();

  // Main routes - differentiated content for each page
  const mainRoutes = [
    {
      url: `${baseUrl}`,
      lastModified: currentDate,
      changeFrequency: 'weekly' as const,
      priority: 1, // Homepage - highest priority
    },
    {
      url: `${baseUrl}/brands`,
      lastModified: currentDate,
      changeFrequency: 'weekly' as const,
      priority: 0.95, // Brands page - very high priority but slightly lower than homepage
    },
    {
      url: `${baseUrl}/agents`,
      lastModified: currentDate,
      changeFrequency: 'weekly' as const,
      priority: 0.9,
    },
    {
      url: `${baseUrl}/users`,
      lastModified: currentDate,
      changeFrequency: 'weekly' as const,
      priority: 0.9,
    },
  ];

  // Content pages - blog and other important content
  const contentPages = [
    {
      url: `${baseUrl}/blog`,
      lastModified: currentDate,
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    },
  ];

  // Legal and support pages
  const legalPages = [
    {
      url: `${baseUrl}/privacy`,
      lastModified: currentDate,
      changeFrequency: 'monthly' as const,
      priority: 0.5,
    },
    {
      url: `${baseUrl}/terms`,
      lastModified: currentDate,
      changeFrequency: 'monthly' as const,
      priority: 0.5,
    },
  ];

  // Combine all routes
  const routes = [
    ...mainRoutes,
    ...contentPages,
    ...legalPages,
  ];

  // Fetch dynamic blog posts from Sanity
  let blogPosts: any[] = [];
  try {
    blogPosts = await client.fetch(`*[_type == "post"]{ slug, publishedAt, updatedAt }`, {}, {
      next: {
        revalidate: 3600, // Revalidate every hour
        tags: ['blog-posts']
      }
    });
  } catch (error) {
    console.error('Error fetching blog posts for sitemap:', error);
  }

  // Add blog post routes
  const blogPostRoutes = blogPosts.map((post) => ({
    url: `${baseUrl}/blog/${post.slug.current}`,
    lastModified: post.updatedAt || post.publishedAt || currentDate,
    changeFrequency: 'weekly' as const,
    priority: 0.7,
  }));

  // Combine all routes including dynamic blog posts
  const allRoutes = [
    ...routes,
    ...blogPostRoutes,
  ];

  return allRoutes;
}
