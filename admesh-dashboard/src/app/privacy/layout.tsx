import { Metadata } from "next";
import { generateMetadata } from "../metadata";

// Privacy page specific metadata
export const metadata: Metadata = generateMetadata(
  "Privacy Policy - AdMesh",
  "AdMesh privacy policy outlining how we collect, use, and protect your data on our AI marketing platform. Learn about our commitment to user privacy and data security.",
  "/privacy",
  undefined // Let the generateMetadata function create the static OG image URL
);

export default function PrivacyLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      {/* Privacy page structured data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPage",
            "@id": "https://useadmesh.com/privacy#webpage",
            name: "AdMesh Privacy Policy",
            description: "Privacy policy for AdMesh AI marketing platform",
            url: "https://useadmesh.com/privacy",
            inLanguage: "en-US",
            isPartOf: {
              "@type": "WebSite",
              "@id": "https://useadmesh.com#website",
              name: "AdM<PERSON>",
              url: "https://useadmesh.com"
            },
            breadcrumb: {
              "@type": "BreadcrumbList",
              itemListElement: [
                {
                  "@type": "ListItem",
                  position: 1,
                  name: "Home",
                  item: "https://useadmesh.com"
                },
                {
                  "@type": "ListItem",
                  position: 2,
                  name: "Privacy Policy",
                  item: "https://useadmesh.com/privacy"
                }
              ]
            },
            mainEntity: {
              "@type": "Article",
              "@id": "https://useadmesh.com/privacy#article",
              headline: "AdMesh Privacy Policy",
              description: "Privacy policy outlining data collection, usage, and protection practices",
              author: {
                "@type": "Organization",
                "@id": "https://useadmesh.com#organization",
                name: "AdMesh"
              },
              publisher: {
                "@type": "Organization",
                "@id": "https://useadmesh.com#organization",
                name: "AdMesh"
              },
              dateModified: new Date().toISOString(),
              datePublished: "2024-01-01T00:00:00Z"
            }
          }, null, 0)
        }}
      />
      {children}
    </>
  );
}
