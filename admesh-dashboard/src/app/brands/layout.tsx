import { Metadata } from "next";
import { generateMetadata } from "../metadata";

// Brands page specific metadata - serves brands content at /brands
export const metadata: Metadata = generateMetadata(
  "AI Marketing Platform for Brands - AdMesh",
  "Connect with high-intent users through AdMesh's AI marketing platform. Place your products directly inside real conversations across AI tools. Performance marketing AI that drives results for brands worldwide.",
  "/brands", // Path - canonical points to /brands since this page serves content here
  undefined // Let the generateMetadata function create the static OG image URL
);

export default function BrandsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      {/* Brands-specific structured data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPage",
            name: "AdMesh for Advertisers - AI Marketing Platform",
            description: "AdMesh is the leading AI marketing platform that connects advertisers with high-intent users through intelligent AI platforms. Revolutionary AI-powered marketing for advertisers.",
            url: "https://useadmesh.com/brands",
            mainEntity: {
              "@type": "SoftwareApplication",
              name: "AdMesh AI Marketing Platform",
              applicationCategory: "BusinessApplication",
              operatingSystem: "Web",
              offers: {
                "@type": "Offer",
                price: "0",
                priceCurrency: "USD",
                description: "Free tier available with premium plans"
              },
              featureList: [
                "AI-powered marketing automation",
                "Performance marketing AI",
                "Real-time customer targeting",
                "Intelligent agent recommendations",
                "Advanced analytics and reporting"
              ],
              audience: {
                "@type": "Audience",
                audienceType: "Advertisers and Marketing Teams"
              }
            },
            breadcrumb: {
              "@type": "BreadcrumbList",
              itemListElement: [
                {
                  "@type": "ListItem",
                  position: 1,
                  name: "Home",
                  item: "https://useadmesh.com"
                },
                {
                  "@type": "ListItem",
                  position: 2,
                  name: "Advertisers",
                  item: "https://useadmesh.com/brands"
                }
              ]
            }
          })
        }}
      />
      {children}
    </>
  );
}
