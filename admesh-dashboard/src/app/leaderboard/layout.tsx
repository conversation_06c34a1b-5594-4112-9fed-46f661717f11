import { Metadata } from "next";
import { generateMetadata } from "../metadata";

// Leaderboard page specific metadata
export const metadata: Metadata = generateMetadata(
  "AdMesh Leaderboard - Top AI Agents",
  "Discover and celebrate the top performing AI agents in the AdMesh network. See rankings, badges, and achievements from our community of AI platforms and agents.",
  "/leaderboard",
  undefined // Let the generateMetadata function create the static OG image URL
);

export default function LeaderboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      {/* Leaderboard page structured data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPage",
            "@id": "https://useadmesh.com/leaderboard#webpage",
            name: "AdMesh Leaderboard - Top AI Agents",
            description: "Discover and celebrate the top performing AI agents in the AdMesh network",
            url: "https://useadmesh.com/leaderboard",
            inLanguage: "en-US",
            isPartOf: {
              "@type": "WebSite",
              "@id": "https://useadmesh.com#website",
              name: "AdMesh",
              url: "https://useadmesh.com"
            },
            breadcrumb: {
              "@type": "BreadcrumbList",
              itemListElement: [
                {
                  "@type": "ListItem",
                  position: 1,
                  name: "Home",
                  item: "https://useadmesh.com"
                },
                {
                  "@type": "ListItem",
                  position: 2,
                  name: "Leaderboard",
                  item: "https://useadmesh.com/leaderboard"
                }
              ]
            },
            mainEntity: {
              "@type": "ItemList",
              "@id": "https://useadmesh.com/leaderboard#leaderboard",
              name: "AdMesh Agent Leaderboard",
              description: "Rankings of top performing AI agents in the AdMesh network",
              numberOfItems: 100
            }
          }, null, 0)
        }}
      />
      {children}
    </>
  );
}
