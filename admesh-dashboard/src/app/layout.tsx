// Split into two files: layout.tsx (server component) and ClientLayout.tsx (client component)
// This is the server component that handles metadata
import "./globals.css";
import { Metadata } from "next";
import { metadata as siteMetadata } from "./metadata";
import ClientLayout from "@/components/ClientLayout";

// Export metadata for the root layout
export const metadata: Metadata = {
  ...siteMetadata,
  // Additional metadata can be added here if needed
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        {/* Favicon - comprehensive setup for all browsers and devices */}
        <link rel="icon" type="image/x-icon" href="/favicon.ico" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16.png" />
        <link rel="shortcut icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/favicon.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <meta name="msapplication-TileColor" content="#000000" />
        <meta name="msapplication-config" content="/browserconfig.xml" />
        <meta name="theme-color" content="#000000" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black" />
        <meta name="apple-mobile-web-app-title" content="AdMesh" />
        {/* Canonical URL will be set by individual page metadata */}

        {/* Schema.org JSON-LD structured data - Organization */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Organization",
              "@id": "https://useadmesh.com#organization",
              name: "AdMesh",
              alternateName: ["useadmesh", "admesh"],
              url: "https://useadmesh.com",
              logo: {
                "@type": "ImageObject",
                url: "https://useadmesh.com/logo.svg",
                width: 48,
                height: 48
              },
              image: "https://useadmesh.com/og-images/root-og.png",
              description: siteMetadata.description,
              foundingDate: "2024",
              sameAs: [
                "https://twitter.com/useadmesh",
                "https://linkedin.com/company/useadmesh"
              ],
              contactPoint: {
                "@type": "ContactPoint",
                contactType: "customer service",
                email: "<EMAIL>",
                availableLanguage: "English"
              },
              address: {
                "@type": "PostalAddress",
                addressCountry: "US"
              }
            }, null, 0)
          }}
        />

        {/* Schema.org JSON-LD structured data - WebSite with Sitelinks */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "WebSite",
              "@id": "https://useadmesh.com#website",
              name: "AdMesh",
              alternateName: ["AdMesh - Promote your products inside AI", "useadmesh", "admesh"],
              url: "https://useadmesh.com",
              description: siteMetadata.description,
              inLanguage: "en-US",
              publisher: {
                "@id": "https://useadmesh.com#organization"
              },
              potentialAction: {
                "@type": "SearchAction",
                target: {
                  "@type": "EntryPoint",
                  urlTemplate: "https://useadmesh.com/search?q={search_term_string}"
                },
                "query-input": "required name=search_term_string"
              },
              mainEntity: {
                "@type": "SoftwareApplication",
                "@id": "https://useadmesh.com#software",
                name: "AdMesh",
                applicationCategory: "BusinessApplication",
                operatingSystem: "Web",
                description: "Leading AI marketing platform and performance marketing AI solution that connects brands with users through intelligent AI agents.",
                url: "https://useadmesh.com",
                offers: {
                  "@type": "Offer",
                  price: "0",
                  priceCurrency: "USD",
                  description: "Free to get started",
                  availability: "https://schema.org/InStock"
                },
                featureList: [
                  "AI-powered product placement",
                  "Performance marketing automation",
                  "Real-time conversation integration",
                  "Agent monetization platform"
                ]
              }
            }, null, 0)
          }}
        />

        {/* Schema.org JSON-LD structured data - SiteNavigationElement */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "ItemList",
              "@id": "https://useadmesh.com#navigation",
              name: "AdMesh Main Navigation",
              itemListElement: [
                {
                  "@type": "SiteNavigationElement",
                  position: 1,
                  name: "For Brands",
                  description: "AI marketing platform for brands - Connect with high-intent users through performance marketing AI",
                  url: "https://useadmesh.com/brands"
                },
                {
                  "@type": "SiteNavigationElement",
                  position: 2,
                  name: "For AI Agents",
                  description: "Join the AdMesh AI marketing platform Agent Pioneer Program",
                  url: "https://useadmesh.com/agents"
                },
                {
                  "@type": "SiteNavigationElement",
                  position: 3,
                  name: "For Users",
                  description: "Discover AI marketing tools and earn rewards through our AI-powered platform",
                  url: "https://useadmesh.com/users"
                },
                {
                  "@type": "SiteNavigationElement",
                  position: 4,
                  name: "Blog",
                  description: "AdMesh blog with AI marketing insights and updates",
                  url: "https://useadmesh.com/blog"
                },
                {
                  "@type": "SiteNavigationElement",
                  position: 5,
                  name: "Documentation",
                  description: "AdMesh API documentation and integration guides",
                  url: "https://docs.useadmesh.com/"
                },
                {
                  "@type": "SiteNavigationElement",
                  position: 6,
                  name: "Terms",
                  description: "AdMesh Terms of Service",
                  url: "https://useadmesh.com/terms"
                },
                {
                  "@type": "SiteNavigationElement",
                  position: 7,
                  name: "Privacy",
                  description: "AdMesh Privacy Policy",
                  url: "https://useadmesh.com/privacy"
                }
              ]
            }, null, 0)
          }}
        />




      </head>
      <body suppressHydrationWarning={true}>
        <ClientLayout>{children}</ClientLayout>
      </body>
    </html>
  );
}
