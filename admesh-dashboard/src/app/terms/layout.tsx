import { Metadata } from "next";
import { generateMetadata } from "../metadata";

// Terms page specific metadata
export const metadata: Metadata = generateMetadata(
  "Terms of Service - AdMesh",
  "AdMesh terms of service governing the use of our AI marketing platform. Review our terms and conditions for brands, agents, and users of the AdMesh ecosystem.",
  "/terms",
  undefined // Let the generateMetadata function create the static OG image URL
);

export default function TermsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      {/* Terms page structured data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPage",
            "@id": "https://useadmesh.com/terms#webpage",
            name: "AdMesh Terms of Service",
            description: "Terms of service for AdMesh AI marketing platform",
            url: "https://useadmesh.com/terms",
            inLanguage: "en-US",
            isPartOf: {
              "@type": "WebSite",
              "@id": "https://useadmesh.com#website",
              name: "AdMesh",
              url: "https://useadmesh.com"
            },
            breadcrumb: {
              "@type": "BreadcrumbList",
              itemListElement: [
                {
                  "@type": "ListItem",
                  position: 1,
                  name: "Home",
                  item: "https://useadmesh.com"
                },
                {
                  "@type": "ListItem",
                  position: 2,
                  name: "Terms of Service",
                  item: "https://useadmesh.com/terms"
                }
              ]
            },
            mainEntity: {
              "@type": "Article",
              "@id": "https://useadmesh.com/terms#article",
              headline: "AdMesh Terms of Service",
              description: "Terms and conditions governing the use of AdMesh platform",
              author: {
                "@type": "Organization",
                "@id": "https://useadmesh.com#organization",
                name: "AdMesh"
              },
              publisher: {
                "@type": "Organization",
                "@id": "https://useadmesh.com#organization",
                name: "AdMesh"
              },
              dateModified: new Date().toISOString(),
              datePublished: "2024-01-01T00:00:00Z"
            }
          }, null, 0)
        }}
      />
      {children}
    </>
  );
}
